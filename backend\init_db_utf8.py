#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本 - UTF-8 编码版本
创建数据库表并添加中文测试数据
"""

import sys
import json
from app.db.database import engine, SessionLocal
from app.models import admin, project
from app.core.security import get_password_hash
from app.models.admin import Admin
from app.models.project import Project, Registration

# 确保使用 UTF-8 编码
sys.stdout.reconfigure(encoding='utf-8')

def init_database():
    """初始化数据库"""
    print("正在创建数据库表...")
    
    # 创建所有表
    admin.Base.metadata.create_all(bind=engine)
    project.Base.metadata.create_all(bind=engine)
    
    print("数据库表创建完成！")

def create_admin_user(username: str = "admin", password: str = "admin123"):
    """创建初始管理员账户"""
    db = SessionLocal()
    try:
        # 检查管理员是否已存在
        existing_admin = db.query(Admin).filter(Admin.username == username).first()
        if existing_admin:
            print(f"管理员 '{username}' 已存在，跳过创建")
            return
        
        # 创建新管理员
        hashed_password = get_password_hash(password)
        new_admin = Admin(
            username=username,
            password=hashed_password
        )
        
        db.add(new_admin)
        db.commit()
        db.refresh(new_admin)
        
        print(f"管理员账户创建成功！")
        print(f"用户名: {username}")
        print(f"密码: {password}")
        
    except Exception as e:
        print(f"创建管理员账户失败: {e}")
        db.rollback()
    finally:
        db.close()

def create_test_projects():
    """创建测试项目和数据"""
    db = SessionLocal()
    try:
        # 创建测试项目
        form_config = {
            "fields": [
                {
                    "name": "name",
                    "label": "姓名",
                    "type": "text",
                    "required": True
                },
                {
                    "name": "phone", 
                    "label": "电话",
                    "type": "text",
                    "required": True
                },
                {
                    "name": "email",
                    "label": "邮箱",
                    "type": "email",
                    "required": False
                }
            ]
        }
        
        # 项目1
        project1 = Project(
            title="活动报名登记",
            description="2025年春节联欢晚会报名",
            form_config=json.dumps(form_config, ensure_ascii=False),
            share_link="spring-festival-2025"
        )
        
        # 项目2  
        project2 = Project(
            title="培训课程登记",
            description="Python编程培训班报名登记",
            form_config=json.dumps(form_config, ensure_ascii=False),
            share_link="python-training-2025"
        )
        
        db.add(project1)
        db.add(project2)
        db.commit()
        db.refresh(project1)
        db.refresh(project2)
        
        # 创建测试登记数据
        test_registrations = [
            {
                "project_id": project1.id,
                "form_data": {
                    "name": "张三",
                    "phone": "13800138000", 
                    "email": "<EMAIL>"
                }
            },
            {
                "project_id": project1.id,
                "form_data": {
                    "name": "李四",
                    "phone": "13900139000",
                    "email": "<EMAIL>"
                }
            },
            {
                "project_id": project2.id,
                "form_data": {
                    "name": "王五",
                    "phone": "13700137000",
                    "email": "<EMAIL>"
                }
            }
        ]
        
        for reg_data in test_registrations:
            registration = Registration(
                project_id=reg_data["project_id"],
                form_data=json.dumps(reg_data["form_data"], ensure_ascii=False)
            )
            db.add(registration)
        
        db.commit()
        print("测试项目和数据创建成功！")
        print(f"项目1: {project1.title} (分享链接: {project1.share_link})")
        print(f"项目2: {project2.title} (分享链接: {project2.share_link})")
        
    except Exception as e:
        print(f"创建测试数据失败: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """主函数"""
    print("=== 项目登记系统数据库初始化 (UTF-8) ===")
    
    # 初始化数据库
    init_database()
    
    # 创建管理员账户
    create_admin_user()
    
    # 创建测试数据
    create_test_projects()
    
    print("=== 初始化完成 ===")

if __name__ == "__main__":
    main()
