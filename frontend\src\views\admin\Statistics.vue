<template>
  <div class="statistics-container">
    <n-card>
      <template #header>
        <n-space justify="space-between" align="center">
          <div>
            <n-h2 style="margin: 0;">数据统计</n-h2>
            <n-text depth="3">{{ projectInfo.title || '项目数据统计' }}</n-text>
          </div>
          <n-space>
            <n-button type="primary" @click="exportExcel" :loading="exportLoading">
              <template #icon>
                <n-icon><download-icon /></n-icon>
              </template>
              导出Excel
            </n-button>
            <n-button @click="refreshData" :loading="loading">
              <template #icon>
                <n-icon><refresh-icon /></n-icon>
              </template>
              刷新数据
            </n-button>
          </n-space>
        </n-space>
      </template>

      <n-spin :show="loading">
        <div v-if="!loading && statistics">
          <!-- 基础统计卡片 -->
          <n-grid :cols="4" :x-gap="16" :y-gap="16" style="margin-bottom: 24px;">
            <n-grid-item>
              <n-card>
                <n-statistic label="总登记数" :value="statistics.total_registrations">
                  <template #prefix>
                    <n-icon color="#18a058"><people-icon /></n-icon>
                  </template>
                </n-statistic>
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card>
                <n-statistic label="今日新增" :value="todayCount">
                  <template #prefix>
                    <n-icon color="#2080f0"><calendar-icon /></n-icon>
                  </template>
                </n-statistic>
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card>
                <n-statistic label="本周新增" :value="weekCount">
                  <template #prefix>
                    <n-icon color="#f0a020"><trending-up-icon /></n-icon>
                  </template>
                </n-statistic>
              </n-card>
            </n-grid-item>
            <n-grid-item>
              <n-card>
                <n-statistic label="平均每日" :value="averageDaily">
                  <template #prefix>
                    <n-icon color="#d03050"><bar-chart-icon /></n-icon>
                  </template>
                </n-statistic>
              </n-card>
            </n-grid-item>
          </n-grid>

          <!-- 日期统计图表 -->
          <n-card title="登记趋势" style="margin-bottom: 24px;">
            <div ref="dateChartRef" style="height: 300px;"></div>
          </n-card>

          <!-- 字段统计 -->
          <n-card title="字段统计">
            <n-grid :cols="2" :x-gap="16" :y-gap="16">
              <n-grid-item v-for="(fieldStat, fieldName) in statistics.field_statistics" :key="fieldName">
                <n-card :title="fieldName" size="small">
                  <div :ref="el => setFieldChartRef(fieldName, el)" style="height: 200px;"></div>
                </n-card>
              </n-grid-item>
            </n-grid>
          </n-card>
        </div>

        <div v-else-if="!loading" style="text-align: center; padding: 40px;">
          <n-empty description="暂无统计数据" />
        </div>
      </n-spin>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, h } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import {
  NCard, NH2, NText, NSpace, NButton, NIcon, NSpin, NGrid, NGridItem,
  NStatistic, NEmpty
} from 'naive-ui'
import { exportApi } from '@/api/export'

// 图标组件
const DownloadIcon = () => h('span', '📥')
const RefreshIcon = () => h('span', '🔄')
const PeopleIcon = () => h('span', '👥')
const CalendarIcon = () => h('span', '📅')
const TrendingUpIcon = () => h('span', '📈')
const BarChartIcon = () => h('span', '📊')

const route = useRoute()
const message = useMessage()

const loading = ref(true)
const exportLoading = ref(false)
const statistics = ref(null)
const projectInfo = ref({})

// 图表引用
const dateChartRef = ref(null)
const fieldChartRefs = ref({})

const setFieldChartRef = (fieldName, el) => {
  if (el) {
    fieldChartRefs.value[fieldName] = el
  }
}

// 计算统计数据
const todayCount = computed(() => {
  if (!statistics.value) return 0
  const today = new Date().toISOString().slice(0, 10)
  return statistics.value.date_statistics[today] || 0
})

const weekCount = computed(() => {
  if (!statistics.value) return 0
  const now = new Date()
  let count = 0
  for (let i = 0; i < 7; i++) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
    const dateStr = date.toISOString().slice(0, 10)
    count += statistics.value.date_statistics[dateStr] || 0
  }
  return count
})

const averageDaily = computed(() => {
  if (!statistics.value) return 0
  const dates = Object.keys(statistics.value.date_statistics)
  if (dates.length === 0) return 0
  const total = Object.values(statistics.value.date_statistics).reduce((sum, count) => sum + count, 0)
  return Math.round(total / dates.length * 10) / 10
})

// 加载统计数据
const loadStatistics = async () => {
  try {
    loading.value = true
    const projectId = route.params.id
    const data = await exportApi.getProjectStatistics(projectId)
    
    statistics.value = data
    projectInfo.value = data.project_info
    
    // 等待DOM更新后渲染图表
    await nextTick()
    renderCharts()
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
    message.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

// 渲染图表（简化版本，使用文本显示）
const renderCharts = () => {
  // 渲染日期趋势图表
  if (dateChartRef.value && statistics.value) {
    renderDateChart()
  }
  
  // 渲染字段统计图表
  Object.keys(statistics.value.field_statistics).forEach(fieldName => {
    const chartEl = fieldChartRefs.value[fieldName]
    if (chartEl) {
      renderFieldChart(fieldName, chartEl)
    }
  })
}

const renderDateChart = () => {
  const dateStats = statistics.value.date_statistics
  const dates = Object.keys(dateStats).sort()
  
  let chartHtml = '<div style="padding: 20px;">'
  chartHtml += '<div style="display: flex; justify-content: space-between; align-items: end; height: 200px;">'
  
  const maxCount = Math.max(...Object.values(dateStats))
  
  dates.forEach(date => {
    const count = dateStats[date]
    const height = maxCount > 0 ? (count / maxCount) * 150 : 0
    const dateLabel = date.slice(5) // MM-DD
    
    chartHtml += `
      <div style="display: flex; flex-direction: column; align-items: center; margin: 0 2px;">
        <div style="background: #18a058; width: 20px; height: ${height}px; margin-bottom: 5px; border-radius: 2px;"></div>
        <div style="font-size: 12px; color: #666;">${dateLabel}</div>
        <div style="font-size: 10px; color: #999;">${count}</div>
      </div>
    `
  })
  
  chartHtml += '</div></div>'
  dateChartRef.value.innerHTML = chartHtml
}

const renderFieldChart = (fieldName, chartEl) => {
  const fieldData = statistics.value.field_statistics[fieldName]
  if (fieldData.type !== 'select') return
  
  const data = fieldData.data
  const total = Object.values(data).reduce((sum, count) => sum + count, 0)
  
  let chartHtml = '<div style="padding: 10px;">'
  
  Object.entries(data).forEach(([label, count]) => {
    const percentage = total > 0 ? Math.round((count / total) * 100) : 0
    chartHtml += `
      <div style="margin-bottom: 8px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 2px;">
          <span style="font-size: 12px;">${label}</span>
          <span style="font-size: 12px; color: #666;">${count} (${percentage}%)</span>
        </div>
        <div style="background: #f0f0f0; height: 6px; border-radius: 3px;">
          <div style="background: #18a058; height: 6px; width: ${percentage}%; border-radius: 3px;"></div>
        </div>
      </div>
    `
  })
  
  chartHtml += '</div>'
  chartEl.innerHTML = chartHtml
}

// 导出Excel
const exportExcel = async () => {
  try {
    exportLoading.value = true
    const projectId = route.params.id
    const result = await exportApi.exportProjectExcel(projectId)
    message.success(`Excel文件导出成功：${result.filename}`)
  } catch (error) {
    console.error('导出Excel失败:', error)
    message.error('导出Excel失败')
  } finally {
    exportLoading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadStatistics()
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.statistics-container {
  padding: 24px;
}
</style>
