<template>
  <div class="form-container">
    <n-card style="max-width: 600px; margin: auto;">
      <template #header>
        <div style="text-align: center;">
          <h2>{{ projectInfo.title || '项目登记表单' }}</h2>
          <p v-if="projectInfo.description">{{ projectInfo.description }}</p>
        </div>
      </template>
      
      <div v-if="loading">
        <p>正在加载...</p>
      </div>
      
      <div v-else-if="error">
        <p style="color: red;">{{ error }}</p>
      </div>
      
      <div v-else>
        <p>项目信息已加载</p>
        <p>字段数量: {{ formFields.length }}</p>
        
        <div v-if="formFields.length > 0">
          <h3>表单字段:</h3>
          <div v-for="field in formFields" :key="field.name" style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #ccc;">
            <p><strong>字段名:</strong> {{ field.name }}</p>
            <p><strong>标签:</strong> {{ field.label }}</p>
            <p><strong>类型:</strong> {{ field.type }}</p>
            <p><strong>必填:</strong> {{ field.required ? '是' : '否' }}</p>
            
            <!-- 简单的输入框 -->
            <input 
              v-if="field.type === 'text' || field.type === 'email' || field.type === 'tel'"
              v-model="formData[field.name]"
              :type="field.type === 'email' ? 'email' : field.type === 'tel' ? 'tel' : 'text'"
              :placeholder="`请输入${field.label}`"
              style="width: 100%; padding: 8px; margin-top: 8px;"
            />
            
            <input 
              v-else-if="field.type === 'number'"
              v-model="formData[field.name]"
              type="number"
              :placeholder="`请输入${field.label}`"
              style="width: 100%; padding: 8px; margin-top: 8px;"
            />
            
            <textarea 
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.name]"
              :placeholder="`请输入${field.label}`"
              rows="3"
              style="width: 100%; padding: 8px; margin-top: 8px;"
            ></textarea>
            
            <input 
              v-else-if="field.type === 'date'"
              v-model="formData[field.name]"
              type="date"
              style="width: 100%; padding: 8px; margin-top: 8px;"
            />
            
            <select 
              v-else-if="field.type === 'select'"
              v-model="formData[field.name]"
              style="width: 100%; padding: 8px; margin-top: 8px;"
            >
              <option value="">请选择{{ field.label }}</option>
              <option 
                v-for="option in field.options" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </option>
            </select>
          </div>
          
          <button @click="handleSubmit" style="padding: 12px 24px; background: #18a058; color: white; border: none; border-radius: 4px; margin-top: 1rem;">
            提交登记
          </button>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { NCard } from 'naive-ui'

const route = useRoute()
const loading = ref(true)
const error = ref('')
const projectInfo = ref({})
const formFields = ref([])
const formData = ref({})

const loadFormConfig = async () => {
  try {
    loading.value = true
    const shareLink = route.params.shareLink
    console.log('分享链接:', shareLink)
    
    const response = await fetch(`/api/form/${shareLink}`)
    console.log('响应状态:', response.status)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    
    const project = await response.json()
    console.log('项目数据:', project)
    
    projectInfo.value = project
    formFields.value = project.form_config?.fields || []
    
    // 初始化表单数据
    const initialData = {}
    formFields.value.forEach(field => {
      initialData[field.name] = ''
    })
    formData.value = initialData
    
    console.log('表单字段:', formFields.value)
    console.log('表单数据:', formData.value)
    
  } catch (err) {
    console.error('加载失败:', err)
    error.value = `加载失败: ${err.message}`
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  try {
    console.log('提交数据:', formData.value)
    
    const shareLink = route.params.shareLink
    const response = await fetch(`/api/form/${shareLink}/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        form_data: formData.value
      })
    })
    
    if (response.ok) {
      alert('提交成功！')
    } else {
      alert('提交失败')
    }
  } catch (err) {
    console.error('提交失败:', err)
    alert('提交失败: ' + err.message)
  }
}

onMounted(() => {
  loadFormConfig()
})
</script>

<style scoped>
.form-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 2rem;
  display: flex;
  align-items: center;
}
</style>
