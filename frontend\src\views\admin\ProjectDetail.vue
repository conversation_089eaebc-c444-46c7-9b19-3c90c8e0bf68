<template>
  <div class="project-detail">
    <n-card>
      <template #header>
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <n-text strong>项目详情</n-text>
          <n-space>
            <n-button type="primary" @click="viewStatistics">
              <template #icon>
                <n-icon>📊</n-icon>
              </template>
              数据统计
            </n-button>
            <n-button @click="exportExcel" :loading="exportLoading">
              <template #icon>
                <n-icon>📥</n-icon>
              </template>
              导出Excel
            </n-button>
            <n-button @click="$router.push(`/admin/projects/${projectId}/edit`)">
              编辑项目
            </n-button>
            <n-button quaternary @click="$router.back()">
              返回
            </n-button>
          </n-space>
        </div>
      </template>

      <n-spin :show="loading">
        <div v-if="project">
          <n-grid :cols="1" :x-gap="16" :y-gap="24">
            <!-- 基本信息 -->
            <n-grid-item>
              <n-card title="基本信息">
                <n-descriptions :column="2" bordered>
                  <n-descriptions-item label="项目标题">
                    {{ project.title }}
                  </n-descriptions-item>
                  <n-descriptions-item label="创建时间">
                    {{ formatDate(project.created_at) }}
                  </n-descriptions-item>
                  <n-descriptions-item label="项目描述" :span="2">
                    {{ project.description }}
                  </n-descriptions-item>
                  <n-descriptions-item label="分享链接" :span="2">
                    <n-space>
                      <n-text code>{{ shareUrl }}</n-text>
                      <n-button size="small" @click="copyShareLink">复制链接</n-button>
                    </n-space>
                  </n-descriptions-item>
                </n-descriptions>
              </n-card>
            </n-grid-item>

            <!-- 表单配置 -->
            <n-grid-item>
              <n-card title="表单配置">
                <n-data-table
                  :columns="fieldColumns"
                  :data="project.form_config?.fields || []"
                  :pagination="false"
                />
              </n-card>
            </n-grid-item>

            <!-- 登记统计 -->
            <n-grid-item>
              <n-card title="登记统计">
                <n-grid :cols="4" :x-gap="16">
                  <n-grid-item>
                    <n-statistic label="总登记数" :value="registrations.length" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-statistic label="今日登记" :value="todayCount" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-statistic label="本周登记" :value="weekCount" />
                  </n-grid-item>
                  <n-grid-item>
                    <n-statistic label="本月登记" :value="monthCount" />
                  </n-grid-item>
                </n-grid>
              </n-card>
            </n-grid-item>

            <!-- 登记记录 -->
            <n-grid-item>
              <n-card>
                <template #header>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <n-text strong>登记记录</n-text>
                    <n-space>
                      <n-button @click="refreshRegistrations" :loading="registrationsLoading">
                        <template #icon>
                          <n-icon>🔄</n-icon>
                        </template>
                        刷新
                      </n-button>
                    </n-space>
                  </div>
                </template>

                <n-data-table
                  :columns="registrationColumns"
                  :data="registrations"
                  :loading="registrationsLoading"
                  :pagination="{ pageSize: 10 }"
                />
              </n-card>
            </n-grid-item>
          </n-grid>
        </div>
      </n-spin>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { projectsApi } from '@/api/projects'
import { exportApi } from '@/api/export'
import {
  NCard, NText, NButton, NSpace, NSpin, NGrid, NGridItem,
  NDescriptions, NDescriptionsItem, NDataTable, NStatistic, NIcon
} from 'naive-ui'

const route = useRoute()
const message = useMessage()

const projectId = computed(() => route.params.id)
const loading = ref(false)
const registrationsLoading = ref(false)
const exportLoading = ref(false)
const project = ref(null)
const registrations = ref([])

// 分享链接
const shareUrl = computed(() => {
  if (!project.value) return ''
  return `${window.location.origin}/form/${project.value.share_link}`
})

// 统计数据
const todayCount = computed(() => {
  const today = new Date().toDateString()
  return registrations.value.filter(reg => 
    new Date(reg.created_at).toDateString() === today
  ).length
})

const weekCount = computed(() => {
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)
  return registrations.value.filter(reg => 
    new Date(reg.created_at) >= weekAgo
  ).length
})

const monthCount = computed(() => {
  const monthAgo = new Date()
  monthAgo.setMonth(monthAgo.getMonth() - 1)
  return registrations.value.filter(reg => 
    new Date(reg.created_at) >= monthAgo
  ).length
})

// 表单字段表格配置
const fieldColumns = [
  { title: '字段名称', key: 'name' },
  { title: '显示标签', key: 'label' },
  { title: '字段类型', key: 'type' },
  { 
    title: '是否必填', 
    key: 'required',
    render: (row) => row.required ? '是' : '否'
  }
]

// 登记记录表格配置
const registrationColumns = [
  {
    title: '姓名',
    key: 'name',
    render: (row) => {
      try {
        const formData = typeof row.form_data === 'string' ? JSON.parse(row.form_data) : row.form_data
        return formData.name || '-'
      } catch {
        return '-'
      }
    }
  },
  {
    title: '电话',
    key: 'phone',
    render: (row) => {
      try {
        const formData = typeof row.form_data === 'string' ? JSON.parse(row.form_data) : row.form_data
        return formData.phone || '-'
      } catch {
        return '-'
      }
    }
  },
  {
    title: '邮箱',
    key: 'email',
    render: (row) => {
      try {
        const formData = typeof row.form_data === 'string' ? JSON.parse(row.form_data) : row.form_data
        return formData.email || '-'
      } catch {
        return '-'
      }
    }
  },
  {
    title: '登记时间',
    key: 'created_at',
    render: (row) => formatDate(row.created_at)
  }
]

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}

// 复制分享链接
const copyShareLink = () => {
  navigator.clipboard.writeText(shareUrl.value).then(() => {
    message.success('分享链接已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败，请手动复制')
  })
}

// 查看统计
const viewStatistics = () => {
  window.open(`/admin/projects/${projectId.value}/statistics`, '_blank')
}

// 导出Excel
const exportExcel = async () => {
  try {
    exportLoading.value = true
    const result = await exportApi.exportProjectExcel(projectId.value)
    message.success(`Excel文件导出成功：${result.filename}`)
  } catch (error) {
    console.error('导出Excel失败:', error)
    message.error('导出Excel失败')
  } finally {
    exportLoading.value = false
  }
}

// 刷新登记记录
const refreshRegistrations = () => {
  loadRegistrations()
}

// 加载项目详情
const loadProject = async () => {
  try {
    loading.value = true
    project.value = await projectsApi.getProject(projectId.value)
  } catch (error) {
    console.error('加载项目失败:', error)
    message.error('加载项目失败')
  } finally {
    loading.value = false
  }
}

// 加载登记记录
const loadRegistrations = async () => {
  try {
    registrationsLoading.value = true
    registrations.value = await projectsApi.getProjectRegistrations(projectId.value)
  } catch (error) {
    console.error('加载登记记录失败:', error)
    message.error('加载登记记录失败')
  } finally {
    registrationsLoading.value = false
  }
}

// 页面加载时执行
onMounted(async () => {
  await loadProject()
  await loadRegistrations()
})
</script>

<style scoped>
.project-detail {
  padding: 24px;
}
</style>
