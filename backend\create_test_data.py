#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试登记数据脚本
用于测试数据导出和统计功能
"""

import json
from datetime import datetime, timedelta
import random
from app.db.database import SessionLocal
from app.models.project import Project, Registration

def create_test_registrations():
    """为动态表单测试项目创建测试登记数据"""
    db = SessionLocal()
    try:
        # 查找测试项目
        project = db.query(Project).filter(Project.share_link == "dynamic-form-test").first()
        if not project:
            print("未找到测试项目，请先运行 create_test_project.py")
            return
        
        # 测试数据模板
        test_data_templates = [
            {
                "name": "张三",
                "phone": "13800138001",
                "email": "<EMAIL>",
                "age": 25,
                "gender": "male",
                "education": "bachelor",
                "birthday": "1998-05-15",
                "address": "北京市朝阳区某某街道123号",
                "introduction": "我是一名软件工程师，热爱编程和技术创新。"
            },
            {
                "name": "李四",
                "phone": "13800138002",
                "email": "<EMAIL>",
                "age": 30,
                "gender": "female",
                "education": "master",
                "birthday": "1993-08-20",
                "address": "上海市浦东新区某某路456号",
                "introduction": "产品经理，专注于用户体验设计和产品优化。"
            },
            {
                "name": "王五",
                "phone": "13800138003",
                "email": "<EMAIL>",
                "age": 28,
                "gender": "male",
                "education": "bachelor",
                "birthday": "1995-12-10",
                "address": "广州市天河区某某大道789号",
                "introduction": "UI/UX设计师，喜欢创造美观实用的界面。"
            },
            {
                "name": "赵六",
                "phone": "13800138004",
                "email": "",
                "age": 22,
                "gender": "female",
                "education": "college",
                "birthday": "2001-03-25",
                "address": "深圳市南山区某某科技园",
                "introduction": "刚毕业的大学生，对互联网行业充满热情。"
            },
            {
                "name": "孙七",
                "phone": "13800138005",
                "email": "<EMAIL>",
                "age": 35,
                "gender": "male",
                "education": "phd",
                "birthday": "1988-07-08",
                "address": "杭州市西湖区某某创业园",
                "introduction": "技术总监，拥有丰富的团队管理和技术架构经验。"
            },
            {
                "name": "周八",
                "phone": "13800138006",
                "email": "<EMAIL>",
                "age": 26,
                "gender": "other",
                "education": "bachelor",
                "birthday": "1997-11-30",
                "address": "成都市高新区某某软件园",
                "introduction": "全栈开发工程师，喜欢学习新技术。"
            },
            {
                "name": "吴九",
                "phone": "13800138007",
                "email": "",
                "age": 29,
                "gender": "female",
                "education": "master",
                "birthday": "1994-04-18",
                "address": "武汉市洪山区某某大学城",
                "introduction": "数据分析师，专注于商业智能和数据挖掘。"
            },
            {
                "name": "郑十",
                "phone": "13800138008",
                "email": "<EMAIL>",
                "age": 24,
                "gender": "male",
                "education": "bachelor",
                "birthday": "1999-09-12",
                "address": "西安市雁塔区某某高新区",
                "introduction": "前端开发工程师，热爱Vue.js和React技术栈。"
            }
        ]
        
        # 创建登记数据，模拟不同时间的提交
        base_time = datetime.now() - timedelta(days=7)
        
        for i, template in enumerate(test_data_templates):
            # 随机时间分布
            random_hours = random.randint(0, 7 * 24)
            created_time = base_time + timedelta(hours=random_hours)
            
            registration = Registration(
                project_id=project.id,
                form_data=json.dumps(template, ensure_ascii=False),
                created_at=created_time
            )
            
            db.add(registration)
        
        db.commit()
        
        print(f"成功创建 {len(test_data_templates)} 条测试登记数据！")
        print(f"项目: {project.title}")
        print(f"数据时间范围: {base_time.strftime('%Y-%m-%d')} 到 {datetime.now().strftime('%Y-%m-%d')}")
        
        # 显示统计信息
        total_registrations = db.query(Registration).filter(Registration.project_id == project.id).count()
        print(f"项目总登记数: {total_registrations}")
        
    except Exception as e:
        print(f"创建测试数据失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_test_registrations()
