<template>
  <div class="dashboard">
    <n-layout has-sider>
      <n-layout-sider bordered collapse-mode="width" :collapsed-width="64" :width="240">
        <n-menu 
          :collapsed="collapsed"
          :options="menuOptions"
          :value="activeKey"
          @update:value="handleMenuSelect"
        />
      </n-layout-sider>
      
      <n-layout>
        <n-layout-header bordered style="height: 64px; padding: 0 24px;">
          <div style="display: flex; align-items: center; justify-content: space-between; height: 100%;">
            <n-button quaternary @click="collapsed = !collapsed">
              ☰
            </n-button>
            
            <n-space>
              <n-text>欢迎，管理员</n-text>
              <n-button @click="handleLogout">退出登录</n-button>
            </n-space>
          </div>
        </n-layout-header>
        
        <n-layout-content style="padding: 24px;">
          <!-- 项目管理页面 -->
          <div v-if="activeKey === 'projects'">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
              <n-h2>项目管理</n-h2>
              <n-button type="primary" @click="handleCreateProject">
                创建新项目
              </n-button>
            </div>

            <n-card>
              <n-data-table
                :columns="columns"
                :data="projects"
                :loading="loading"
                :pagination="pagination"
              />
            </n-card>
          </div>

          <!-- 数据统计页面 -->
          <div v-if="activeKey === 'statistics'">
            <n-h2 style="margin-bottom: 24px;">数据统计</n-h2>

            <n-grid :cols="4" :x-gap="16" :y-gap="16" style="margin-bottom: 24px;">
              <n-grid-item>
                <n-card>
                  <n-statistic label="总项目数" :value="statisticsData.totalProjects" />
                </n-card>
              </n-grid-item>
              <n-grid-item>
                <n-card>
                  <n-statistic label="总登记数" :value="statisticsData.totalRegistrations" />
                </n-card>
              </n-grid-item>
              <n-grid-item>
                <n-card>
                  <n-statistic label="今日登记" :value="statisticsData.todayRegistrations" />
                </n-card>
              </n-grid-item>
              <n-grid-item>
                <n-card>
                  <n-statistic label="活跃项目" :value="statisticsData.activeProjects" />
                </n-card>
              </n-grid-item>
            </n-grid>

            <n-card title="最近登记记录">
              <n-data-table
                :columns="registrationColumns"
                :data="recentRegistrations"
                :loading="statisticsLoading"
                :pagination="{ pageSize: 5 }"
              />
            </n-card>
          </div>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<script setup>
import { ref, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
// import { MenuOutline } from '@vicons/ionicons5'
import { projectsApi } from '@/api/projects'
import {
  NLayout, NLayoutSider, NLayoutHeader, NLayoutContent,
  NMenu, NButton, NIcon, NSpace, NText, NH2, NDivider,
  NCard, NDataTable, NGrid, NGridItem, NStatistic
} from 'naive-ui'

const router = useRouter()
const message = useMessage()

const collapsed = ref(false)
const activeKey = ref('projects')
const loading = ref(false)
const statisticsLoading = ref(false)
const projects = ref([])
const recentRegistrations = ref([])
const statisticsData = ref({
  totalProjects: 0,
  totalRegistrations: 0,
  todayRegistrations: 0,
  activeProjects: 0
})

// 表格配置
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '项目标题',
    key: 'title'
  },
  {
    title: '项目描述',
    key: 'description'
  },
  {
    title: '分享链接',
    key: 'share_link',
    render: (row) => h('div', [
      h('code', { style: 'font-size: 12px; margin-right: 8px;' }, row.share_link),
      h(NButton, {
        size: 'tiny',
        type: 'primary',
        onClick: () => handleCopyShareLink(row.share_link)
      }, '复制')
    ])
  },
  {
    title: '创建时间',
    key: 'created_at',
    render: (row) => new Date(row.created_at).toLocaleString()
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) => h('div', [
      h(NButton, {
        size: 'small',
        style: 'margin-right: 8px;',
        onClick: () => handleViewProject(row.id)
      }, '查看'),
      h(NButton, {
        size: 'small',
        type: 'primary',
        style: 'margin-right: 8px;',
        onClick: () => handleEditProject(row.id)
      }, '编辑'),
      h(NButton, {
        size: 'small',
        type: 'error',
        onClick: () => handleDeleteProject(row.id)
      }, '删除')
    ])
  }
]

const pagination = {
  pageSize: 10
}

// 登记记录表格配置
const registrationColumns = [
  {
    title: '项目',
    key: 'project_title',
    render: (row) => row.project?.title || '未知项目'
  },
  {
    title: '姓名',
    key: 'name',
    render: (row) => {
      try {
        const formData = typeof row.form_data === 'string' ? JSON.parse(row.form_data) : row.form_data
        return formData.name || '-'
      } catch {
        return '-'
      }
    }
  },
  {
    title: '电话',
    key: 'phone',
    render: (row) => {
      try {
        const formData = typeof row.form_data === 'string' ? JSON.parse(row.form_data) : row.form_data
        return formData.phone || '-'
      } catch {
        return '-'
      }
    }
  },
  {
    title: '登记时间',
    key: 'created_at',
    render: (row) => new Date(row.created_at).toLocaleString()
  }
]

const menuOptions = [
  {
    label: '项目管理',
    key: 'projects',
    icon: () => h('span', '📋')
  },
  {
    label: '数据统计',
    key: 'statistics',
    icon: () => h('span', '📊')
  }
]

// 获取项目列表
const fetchProjects = async () => {
  try {
    loading.value = true
    const response = await projectsApi.getProjects()
    projects.value = response

    // 更新统计数据
    statisticsData.value.totalProjects = response.length
    statisticsData.value.activeProjects = response.filter(p => p.registrations?.length > 0).length
  } catch (error) {
    console.error('获取项目列表失败:', error)
    message.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    statisticsLoading.value = true

    // 获取所有项目的登记信息
    let totalRegistrations = 0
    let todayRegistrations = 0
    const today = new Date().toDateString()
    const allRegistrations = []

    for (const project of projects.value) {
      try {
        const registrations = await projectsApi.getProjectRegistrations(project.id)
        totalRegistrations += registrations.length

        // 统计今日登记
        const todayRegs = registrations.filter(reg =>
          new Date(reg.created_at).toDateString() === today
        )
        todayRegistrations += todayRegs.length

        // 收集最近的登记记录
        registrations.forEach(reg => {
          allRegistrations.push({
            ...reg,
            project: project
          })
        })
      } catch (error) {
        console.error(`获取项目 ${project.id} 的登记信息失败:`, error)
      }
    }

    // 更新统计数据
    statisticsData.value.totalRegistrations = totalRegistrations
    statisticsData.value.todayRegistrations = todayRegistrations

    // 按时间排序，取最近的记录
    allRegistrations.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    recentRegistrations.value = allRegistrations.slice(0, 10)

  } catch (error) {
    console.error('获取统计数据失败:', error)
    message.error('获取统计数据失败')
  } finally {
    statisticsLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await fetchProjects()
  if (projects.value.length > 0) {
    await fetchStatistics()
  }
})

const handleMenuSelect = async (key) => {
  activeKey.value = key

  // 切换到统计页面时刷新数据
  if (key === 'statistics' && projects.value.length > 0) {
    await fetchStatistics()
  }
}

const handleCreateProject = () => {
  router.push('/admin/projects/create')
}

const handleViewProject = (projectId) => {
  router.push(`/admin/projects/${projectId}`)
}

const handleEditProject = (projectId) => {
  router.push(`/admin/projects/${projectId}/edit`)
}

const handleDeleteProject = async (projectId) => {
  // 确认删除
  const project = projects.value.find(p => p.id === projectId)
  if (!project) return

  try {
    await projectsApi.deleteProject(projectId)
    message.success('项目删除成功')

    // 重新加载项目列表
    await fetchProjects()

    // 如果有统计数据，也重新加载
    if (projects.value.length > 0) {
      await fetchStatistics()
    }
  } catch (error) {
    console.error('删除项目失败:', error)
    message.error('删除项目失败')
  }
}

const handleCopyShareLink = (shareLink) => {
  const url = `${window.location.origin}/form/${shareLink}`
  navigator.clipboard.writeText(url).then(() => {
    message.success('分享链接已复制到剪贴板')
  }).catch(() => {
    message.error('复制失败，请手动复制')
  })
}

const handleLogout = () => {
  localStorage.removeItem('admin_token')
  message.success('已退出登录')
  router.push('/admin')
}
</script>

<style scoped>
.dashboard {
  height: 100vh;
}
</style>
