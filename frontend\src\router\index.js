import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/admin',
    name: 'AdminLogin',
    component: () => import('@/views/admin/Login.vue')
  },
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: () => import('@/views/admin/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/projects/create',
    name: 'CreateProject',
    component: () => import('@/views/admin/ProjectForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/projects/:id/edit',
    name: 'EditProject',
    component: () => import('@/views/admin/ProjectForm.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/projects/:id',
    name: 'ProjectDetail',
    component: () => import('@/views/admin/ProjectDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/form/:shareLink',
    name: 'CustomerForm',
    component: () => import('@/views/customer/Form.vue')
  },
  {
    path: '/simple-form/:shareLink',
    name: 'SimpleForm',
    component: () => import('@/views/customer/SimpleForm.vue')
  },
  {
    path: '/debug',
    name: 'DebugForm',
    component: () => import('@/views/customer/DebugForm.vue')
  },
  {
    path: '/minimal/:shareLink?',
    name: 'MinimalForm',
    component: () => import('@/views/customer/MinimalForm.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    // 检查是否已登录
    const token = localStorage.getItem('admin_token')
    if (!token) {
      next('/admin')
      return
    }
  }
  next()
})

export default router
