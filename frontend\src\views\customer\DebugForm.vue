<template>
  <div class="debug-container">
    <h1>API调试页面</h1>
    
    <div style="margin: 20px 0;">
      <button @click="testDirectFetch">测试直接fetch调用</button>
      <button @click="testAxiosCall">测试axios调用</button>
    </div>
    
    <div v-if="loading">加载中...</div>
    
    <div v-if="error" style="color: red;">
      <h3>错误信息:</h3>
      <pre>{{ error }}</pre>
    </div>
    
    <div v-if="result">
      <h3>结果:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { formsApi } from '@/api/forms'

const loading = ref(false)
const error = ref('')
const result = ref(null)

const testDirectFetch = async () => {
  try {
    loading.value = true
    error.value = ''
    result.value = null
    
    console.log('开始直接fetch测试')
    const response = await fetch('/api/form/dynamic-form-test')
    console.log('fetch响应状态:', response.status)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    
    const data = await response.json()
    console.log('fetch获取的数据:', data)
    result.value = { type: 'fetch', data }
    
  } catch (err) {
    console.error('fetch测试失败:', err)
    error.value = `fetch测试失败: ${err.message}`
  } finally {
    loading.value = false
  }
}

const testAxiosCall = async () => {
  try {
    loading.value = true
    error.value = ''
    result.value = null
    
    console.log('开始axios测试')
    const data = await formsApi.getFormConfig('dynamic-form-test')
    console.log('axios获取的数据:', data)
    result.value = { type: 'axios', data }
    
  } catch (err) {
    console.error('axios测试失败:', err)
    error.value = `axios测试失败: ${err.message}`
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

button {
  margin-right: 10px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
