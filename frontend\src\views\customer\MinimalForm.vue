<template>
  <div style="padding: 20px; max-width: 600px; margin: 0 auto;">
    <h1>{{ projectInfo.title || '加载中...' }}</h1>
    <p>{{ projectInfo.description }}</p>
    
    <div style="background: #f0f0f0; padding: 10px; margin: 10px 0;">
      <strong>调试信息:</strong><br>
      页面加载: {{ pageLoading }}<br>
      字段数量: {{ formFields.length }}<br>
      项目ID: {{ projectInfo.id }}<br>
      form_config存在: {{ !!projectInfo.form_config }}<br>
      <div v-if="projectInfo.form_config">
        fields存在: {{ !!projectInfo.form_config.fields }}<br>
        fields长度: {{ projectInfo.form_config.fields?.length }}
      </div>
    </div>
    
    <div v-if="pageLoading">
      <p>正在加载...</p>
    </div>
    
    <div v-else-if="formFields.length === 0">
      <p style="color: red;">没有找到表单字段</p>
      <button @click="loadFormConfig">重新加载</button>
    </div>
    
    <div v-else>
      <h3>表单字段 ({{ formFields.length }}个):</h3>
      <div v-for="field in formFields" :key="field.name" style="margin: 10px 0; padding: 10px; border: 1px solid #ccc;">
        <label>{{ field.label }} ({{ field.type }})</label>
        <input 
          v-model="formData[field.name]"
          :type="field.type === 'email' ? 'email' : field.type === 'tel' ? 'tel' : field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'"
          style="width: 100%; padding: 5px; margin-top: 5px;"
        />
      </div>
      
      <button @click="handleSubmit" style="padding: 10px 20px; background: #007bff; color: white; border: none; margin-top: 10px;">
        提交
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const pageLoading = ref(true)
const projectInfo = ref({})
const formFields = ref([])
const formData = ref({})

const loadFormConfig = async () => {
  try {
    pageLoading.value = true
    const shareLink = route.params.shareLink || 'dynamic-form-test'
    
    console.log('=== 开始加载表单配置 ===')
    console.log('分享链接:', shareLink)
    
    const response = await fetch(`/api/form/${shareLink}`)
    console.log('响应状态:', response.status)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    
    const project = await response.json()
    console.log('=== 获取到的原始数据 ===')
    console.log(JSON.stringify(project, null, 2))
    
    // 设置项目信息
    projectInfo.value = project
    console.log('设置项目信息后:', projectInfo.value)
    
    // 提取表单字段
    const fields = project.form_config?.fields || []
    console.log('提取的字段:', fields)
    console.log('字段数量:', fields.length)
    
    formFields.value = fields
    console.log('设置字段后 formFields.value:', formFields.value)
    console.log('formFields.value.length:', formFields.value.length)
    
    // 初始化表单数据
    const initialData = {}
    fields.forEach(field => {
      initialData[field.name] = ''
    })
    formData.value = initialData
    console.log('初始化表单数据:', formData.value)
    
    console.log('=== 加载完成 ===')
    
  } catch (error) {
    console.error('加载失败:', error)
  } finally {
    pageLoading.value = false
    console.log('设置 pageLoading = false')
  }
}

const handleSubmit = () => {
  console.log('提交数据:', formData.value)
  alert('提交成功！数据: ' + JSON.stringify(formData.value))
}

onMounted(() => {
  console.log('组件挂载，开始加载')
  loadFormConfig()
})
</script>
