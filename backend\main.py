from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api import admin, projects, forms
from app.db.database import engine
from app.models import admin as admin_models, project as project_models

# 创建数据库表
admin_models.Base.metadata.create_all(bind=engine)
project_models.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="项目登记系统 API",
    description="一个简单易用的项目登记管理系统",
    version="1.0.0"
)

# 设置响应编码
from fastapi.responses import JSONResponse
from fastapi import Request
import json

@app.middleware("http")
async def add_charset_middleware(request: Request, call_next):
    response = await call_next(request)
    if response.headers.get("content-type", "").startswith("application/json"):
        response.headers["content-type"] = "application/json; charset=utf-8"
    return response

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(admin.router, prefix="/api/admin", tags=["管理员"])
app.include_router(projects.router, prefix="/api/projects", tags=["项目"])
app.include_router(forms.router, prefix="/api/form", tags=["表单"])

@app.get("/")
async def root():
    return {"message": "项目登记系统 API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
