#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试项目脚本
用于测试动态表单功能
"""

import json
from app.db.database import SessionLocal
from app.models.project import Project

def create_dynamic_form_test_project():
    """创建动态表单测试项目"""
    db = SessionLocal()
    try:
        # 复杂表单配置
        form_config = {
            "fields": [
                {
                    "name": "name",
                    "label": "姓名",
                    "type": "text",
                    "required": True,
                    "options": []
                },
                {
                    "name": "phone",
                    "label": "联系电话",
                    "type": "tel",
                    "required": True,
                    "options": []
                },
                {
                    "name": "email",
                    "label": "邮箱地址",
                    "type": "email",
                    "required": False,
                    "options": []
                },
                {
                    "name": "age",
                    "label": "年龄",
                    "type": "number",
                    "required": True,
                    "options": []
                },
                {
                    "name": "gender",
                    "label": "性别",
                    "type": "select",
                    "required": True,
                    "options": [
                        {"label": "男", "value": "male"},
                        {"label": "女", "value": "female"},
                        {"label": "其他", "value": "other"}
                    ]
                },
                {
                    "name": "education",
                    "label": "学历",
                    "type": "select",
                    "required": False,
                    "options": [
                        {"label": "高中及以下", "value": "high_school"},
                        {"label": "大专", "value": "college"},
                        {"label": "本科", "value": "bachelor"},
                        {"label": "硕士", "value": "master"},
                        {"label": "博士", "value": "phd"}
                    ]
                },
                {
                    "name": "birthday",
                    "label": "出生日期",
                    "type": "date",
                    "required": False,
                    "options": []
                },
                {
                    "name": "address",
                    "label": "详细地址",
                    "type": "textarea",
                    "required": False,
                    "options": []
                },
                {
                    "name": "introduction",
                    "label": "自我介绍",
                    "type": "textarea",
                    "required": False,
                    "options": []
                }
            ]
        }
        
        # 创建测试项目
        test_project = Project(
            title="动态表单功能测试",
            description="这是一个用于测试各种表单字段类型的项目，包含文本、数字、选择、日期等多种字段类型",
            form_config=json.dumps(form_config, ensure_ascii=False),
            share_link="dynamic-form-test"
        )
        
        db.add(test_project)
        db.commit()
        db.refresh(test_project)
        
        print("动态表单测试项目创建成功！")
        print(f"项目标题: {test_project.title}")
        print(f"分享链接: {test_project.share_link}")
        print(f"访问地址: http://localhost:3001/form/{test_project.share_link}")
        
        return test_project
        
    except Exception as e:
        print(f"创建测试项目失败: {e}")
        db.rollback()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    create_dynamic_form_test_project()
