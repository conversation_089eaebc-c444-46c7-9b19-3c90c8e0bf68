<template>
  <div class="form-container">
    <n-card style="max-width: 600px; margin: auto;">
      <template #header>
        <div style="text-align: center;">
          <n-h2>{{ projectInfo.title || '项目登记表单' }}</n-h2>
          <n-text depth="3" v-if="projectInfo.description">
            {{ projectInfo.description }}
          </n-text>
        </div>
      </template>

      <n-spin :show="pageLoading">
        <div v-if="!pageLoading">
          <n-alert type="info" style="margin-bottom: 1.5rem;">
            请填写以下信息完成登记
          </n-alert>

          <!-- 调试信息 -->
          <div v-if="formFields.length === 0" style="text-align: center; padding: 2rem;">
            <n-text depth="3">正在加载表单字段...</n-text>
            <br><br>
            <n-text depth="3">调试信息：字段数量 {{ formFields.length }}</n-text>
          </div>

          <n-form v-else ref="formRef" :model="formData" :rules="dynamicRules" label-placement="top">
            <!-- 动态渲染表单字段 -->
            <n-form-item
              v-for="field in formFields"
              :key="field.name"
              :label="field.label"
              :path="field.name"
            >
              <!-- 文本输入框 -->
              <n-input
                v-if="field.type === 'text'"
                v-model:value="formData[field.name]"
                :placeholder="`请输入${field.label}`"
                size="large"
              />

              <!-- 邮箱输入框 -->
              <n-input
                v-else-if="field.type === 'email'"
                v-model:value="formData[field.name]"
                :placeholder="`请输入${field.label}`"
                size="large"
                type="email"
              />

              <!-- 电话输入框 -->
              <n-input
                v-else-if="field.type === 'tel'"
                v-model:value="formData[field.name]"
                :placeholder="`请输入${field.label}`"
                size="large"
                type="tel"
              />

              <!-- 数字输入框 -->
              <n-input
                v-else-if="field.type === 'number'"
                v-model:value="formData[field.name]"
                :placeholder="`请输入${field.label}`"
                size="large"
                type="number"
              />

              <!-- 多行文本 -->
              <n-input
                v-else-if="field.type === 'textarea'"
                v-model:value="formData[field.name]"
                type="textarea"
                :placeholder="`请输入${field.label}`"
                :rows="3"
                size="large"
              />

              <!-- 日期选择 -->
              <n-input
                v-else-if="field.type === 'date'"
                v-model:value="formData[field.name]"
                type="date"
                :placeholder="`请选择${field.label}`"
                size="large"
              />

              <!-- 选择框 -->
              <n-select
                v-else-if="field.type === 'select'"
                v-model:value="formData[field.name]"
                :options="field.options || []"
                :placeholder="`请选择${field.label}`"
                size="large"
              />

              <!-- 默认文本输入 -->
              <n-input
                v-else
                v-model:value="formData[field.name]"
                :placeholder="`请输入${field.label}`"
                size="large"
              />
            </n-form-item>

            <n-form-item style="margin-top: 2rem;">
              <n-space justify="center" style="width: 100%;">
                <n-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  @click="handleSubmit"
                  style="min-width: 120px;"
                >
                  提交登记
                </n-button>
                <n-button size="large" @click="handleReset" style="min-width: 120px;">
                  重置
                </n-button>
              </n-space>
            </n-form-item>
          </n-form>
        </div>
      </n-spin>
    </n-card>

    <!-- 提交成功对话框 -->
    <n-modal v-model:show="showSuccessModal" :mask-closable="false">
      <n-card style="width: 400px;" title="提交成功" :bordered="false" size="huge">
        <template #header-extra>
          <n-button quaternary circle @click="showSuccessModal = false">
            ✕
          </n-button>
        </template>

        <div style="text-align: center; padding: 20px 0;">
          <n-icon size="64" color="#18a058" style="margin-bottom: 16px;">
            ✓
          </n-icon>
          <n-h3>登记成功！</n-h3>
          <n-text depth="3">您的信息已成功提交，感谢您的参与！</n-text>
        </div>

        <template #footer>
          <div style="text-align: center;">
            <n-button type="primary" @click="handleNewSubmission">
              继续登记
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage } from 'naive-ui'
import { formsApi } from '@/api/forms'
import {
  NCard, NText, NAlert, NForm, NFormItem, NInput, NButton, NSpace,
  NSpin, NH2, NSelect, NModal, NIcon, NH3
} from 'naive-ui'

const route = useRoute()
const message = useMessage()
const formRef = ref(null)
const loading = ref(false)
const pageLoading = ref(true)
const showSuccessModal = ref(false)

// 项目信息和表单配置
const projectInfo = ref({})
const formFields = ref([])
const formData = ref({})

// 动态生成验证规则
const dynamicRules = computed(() => {
  const rules = {}
  formFields.value.forEach(field => {
    const fieldRules = []

    // 必填验证
    if (field.required) {
      fieldRules.push({
        required: true,
        message: `请${field.type === 'select' || field.type === 'date' ? '选择' : '输入'}${field.label}`,
        trigger: field.type === 'select' || field.type === 'date' ? 'change' : 'blur'
      })
    }

    // 邮箱格式验证
    if (field.type === 'email') {
      fieldRules.push({
        type: 'email',
        message: '请输入正确的邮箱格式',
        trigger: 'blur'
      })
    }

    // 电话号码验证
    if (field.type === 'tel') {
      fieldRules.push({
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      })
    }

    // 数字范围验证
    if (field.type === 'number') {
      fieldRules.push({
        type: 'number',
        message: '请输入有效的数字',
        trigger: 'blur'
      })
    }

    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  return rules
})

// 加载表单配置
const loadFormConfig = async () => {
  try {
    pageLoading.value = true
    const shareLink = route.params.shareLink
    console.log('开始加载表单配置，分享链接:', shareLink)

    // 获取项目和表单配置
    const project = await formsApi.getFormConfig(shareLink)
    console.log('获取到项目数据:', project)

    projectInfo.value = project
    formFields.value = project.form_config?.fields || []
    console.log('表单字段:', formFields.value)

    // 初始化表单数据
    const initialData = {}
    formFields.value.forEach(field => {
      initialData[field.name] = field.type === 'number' ? null : ''
    })
    formData.value = initialData
    console.log('初始化表单数据:', formData.value)

  } catch (error) {
    console.error('加载表单配置失败:', error)
    message.error('表单加载失败，请检查链接是否正确')
  } finally {
    pageLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const shareLink = route.params.shareLink

    // 处理日期字段
    const submitData = { ...formData.value }
    formFields.value.forEach(field => {
      if (field.type === 'date' && submitData[field.name]) {
        // 将时间戳转换为日期字符串
        submitData[field.name] = new Date(submitData[field.name]).toISOString().split('T')[0]
      }
    })

    await formsApi.submitForm(shareLink, submitData)

    showSuccessModal.value = true

  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  const initialData = {}
  formFields.value.forEach(field => {
    initialData[field.name] = field.type === 'number' ? null : ''
  })
  formData.value = initialData
}

// 继续登记
const handleNewSubmission = () => {
  showSuccessModal.value = false
  handleReset()
}

// 页面加载时执行
onMounted(() => {
  loadFormConfig()
})
</script>

<style scoped>
.form-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 2rem;
  display: flex;
  align-items: center;
}
</style>
